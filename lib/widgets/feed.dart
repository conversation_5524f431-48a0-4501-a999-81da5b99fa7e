import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/posts_provider.dart';
import '../theme/app_theme.dart';
import 'feed_item.dart';

class Feed extends StatefulWidget {
  const Feed({super.key});

  @override
  State<Feed> createState() => _FeedState();
}

class _FeedState extends State<Feed> {
  final PageController _pageController = PageController();
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    // Load posts when the widget is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      if (postsProvider.posts.isEmpty) {
        postsProvider.loadPosts();
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PostsProvider>(
      builder: (context, postsProvider, child) {
        if (postsProvider.status == PostsStatus.loading &&
            postsProvider.posts.isEmpty) {
          return const Scaffold(
            backgroundColor: Colors.black,
            body: Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
              ),
            ),
          );
        }

        if (postsProvider.status == PostsStatus.error) {
          return Scaffold(
            backgroundColor: Colors.black,
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.white,
                    size: 64,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading posts',
                    style: Theme.of(
                      context,
                    ).textTheme.headlineSmall?.copyWith(color: Colors.white),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    postsProvider.errorMessage ?? 'Unknown error occurred',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.copyWith(color: Colors.white70),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => postsProvider.loadPosts(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.gfGreen,
                      foregroundColor: Colors.black,
                    ),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        if (postsProvider.posts.isEmpty) {
          return const Scaffold(
            backgroundColor: Colors.black,
            body: Center(
              child: Text(
                'No posts available',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ),
          );
        }

        return Scaffold(
          backgroundColor: Colors.black,
          body: GestureDetector(
            onPanUpdate: (details) {
              // Handle mouse drag scrolling for Windows
              if (kDebugMode) {
                print('Feed: Pan detected - dy: ${details.delta.dy}');
              }
            },
            onPanEnd: (details) {
              // Determine scroll direction based on velocity
              final velocity = details.velocity.pixelsPerSecond.dy;
              if (kDebugMode) {
                print('Feed: Pan ended - velocity: $velocity');
              }

              if (velocity > 500) {
                // Fast swipe down - go to previous page
                if (_currentIndex > 0) {
                  _pageController.previousPage(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                }
              } else if (velocity < -500) {
                // Fast swipe up - go to next page
                if (_currentIndex < postsProvider.posts.length - 1) {
                  _pageController.nextPage(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                }
              }
            },
            child: PageView.builder(
              controller: _pageController,
              scrollDirection: Axis.vertical,
              itemCount: postsProvider.posts.length,
              physics: const ClampingScrollPhysics(),
              onPageChanged: (index) {
                if (kDebugMode) {
                  print('Feed: Page changed to index $index');
                }
                setState(() {
                  _currentIndex = index;
                });

                // Load more posts when approaching the end
                if (index >= postsProvider.posts.length - 2 &&
                    postsProvider.hasMore &&
                    postsProvider.status != PostsStatus.loading) {
                  postsProvider.loadMorePosts();
                }
              },
              itemBuilder: (context, index) {
                if (index >= postsProvider.posts.length) {
                  return const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.gfGreen,
                      ),
                    ),
                  );
                }

                final post = postsProvider.posts[index];
                return FeedItem(
                  post: post,
                  isVisible: index == _currentIndex,
                );
              },
            ),
          ),
        );
      },
    );
  }
}
